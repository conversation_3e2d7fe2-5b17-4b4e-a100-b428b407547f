#!/usr/bin/env python3
"""
结构化输出验证测试脚本
测试智能体是否正确使用设置好的结构化输出格式
"""

import asyncio
import json
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from 服务.LangChain_智能体服务 import LangChain智能体服务实例


async def 测试结构化输出格式():
    """测试结构化输出格式是否正确应用"""
    print("=" * 60)
    print("🧪 开始测试结构化输出格式")
    print("=" * 60)
    
    # 测试用例列表
    测试用例 = [
        {
            "名称": "基础问答测试",
            "用户消息": "你们公司主要做什么业务？",
            "期望字段": ["我的回答", "用户问题"]
        },
        {
            "名称": "产品咨询测试", 
            "用户消息": "你们有什么产品可以推荐？",
            "期望字段": ["我的回答", "用户问题"]
        },
        {
            "名称": "服务询问测试",
            "用户消息": "你们提供哪些服务？",
            "期望字段": ["我的回答", "用户问题"]
        }
    ]
    
    for i, 用例 in enumerate(测试用例, 1):
        print(f"\n📋 测试用例 {i}: {用例['名称']}")
        print(f"💬 用户消息: {用例['用户消息']}")
        
        try:
            # 调用智能体
            对话结果 = await LangChain智能体服务实例.智能体对话(
                智能体id=5,
                用户表id=3,
                用户消息=用例['用户消息'],
                会话id=f"test-structured-output-{i}",
                自定义变量={"我方微信号id": 6, "识别id": 1},
            )
            
            # 检查对话是否成功
            if 对话结果['status'] != 100:
                print(f"❌ 对话失败: {对话结果.get('message', '未知错误')}")
                continue
                
            # 获取智能体回复
            智能体回复 = 对话结果['data']['智能体回复']
            结构化输出状态 = 对话结果['data']['结构化输出']
            
            print(f"🔧 结构化输出状态: {结构化输出状态}")
            print(f"🤖 智能体回复: {智能体回复}")
            
            # 验证结构化输出状态
            if not 结构化输出状态:
                print("❌ 结构化输出未启用")
                continue
                
            # 尝试解析JSON回复
            try:
                回复数据 = json.loads(智能体回复)
                print(f"✅ JSON解析成功")
                
                # 验证必需字段
                缺失字段 = []
                for 字段 in 用例['期望字段']:
                    if 字段 not in 回复数据:
                        缺失字段.append(字段)
                        
                if 缺失字段:
                    print(f"❌ 缺失必需字段: {缺失字段}")
                else:
                    print(f"✅ 所有必需字段都存在: {用例['期望字段']}")
                    
                # 验证字段内容
                for 字段, 值 in 回复数据.items():
                    print(f"  📝 {字段}: {值}")
                    
                # 验证用户问题字段是否正确
                if "用户问题" in 回复数据:
                    if 回复数据["用户问题"] == 用例['用户消息']:
                        print("✅ 用户问题字段内容正确")
                    else:
                        print(f"⚠️ 用户问题字段内容不匹配")
                        print(f"   期望: {用例['用户消息']}")
                        print(f"   实际: {回复数据['用户问题']}")
                        
                # 验证回答字段是否有内容
                if "我的回答" in 回复数据:
                    回答内容 = 回复数据["我的回答"].strip()
                    if len(回答内容) > 10:  # 至少10个字符
                        print("✅ 回答内容充实")
                    else:
                        print("⚠️ 回答内容过短")
                        
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"   原始回复: {智能体回复}")
                
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            
        print("-" * 50)


async def 测试JSON_Schema验证():
    """测试JSON Schema是否正确配置"""
    print("\n🔍 验证JSON Schema配置")
    print("=" * 40)
    
    try:
        # 获取智能体详情
        智能体详情 = await LangChain智能体服务实例.获取智能体详情(5)
        
        if not 智能体详情:
            print("❌ 无法获取智能体详情")
            return
            
        自定义回复格式 = 智能体详情.get('自定义回复格式')
        
        if not 自定义回复格式:
            print("❌ 智能体未配置自定义回复格式")
            return
            
        print("✅ 智能体已配置自定义回复格式")
        print(f"📋 JSON Schema: {json.dumps(自定义回复格式, ensure_ascii=False, indent=2)}")
        
        # 验证Schema结构
        必需字段 = ["type", "properties", "required"]
        for 字段 in 必需字段:
            if 字段 in 自定义回复格式:
                print(f"✅ Schema包含必需字段: {字段}")
            else:
                print(f"❌ Schema缺失必需字段: {字段}")
                
        # 验证properties字段
        if "properties" in 自定义回复格式:
            属性字段 = 自定义回复格式["properties"]
            print(f"📝 定义的属性字段: {list(属性字段.keys())}")
            
            for 字段名, 字段定义 in 属性字段.items():
                if "description" in 字段定义:
                    print(f"  ✅ {字段名}: {字段定义['description']}")
                else:
                    print(f"  ⚠️ {字段名}: 缺少描述")
                    
    except Exception as e:
        print(f"❌ Schema验证异常: {str(e)}")


async def main():
    """主函数"""
    print("🚀 结构化输出验证测试开始")
    
    # 测试JSON Schema配置
    await 测试JSON_Schema验证()
    
    # 测试结构化输出格式
    await 测试结构化输出格式()
    
    print("\n" + "=" * 60)
    print("🎉 结构化输出验证测试完成")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
